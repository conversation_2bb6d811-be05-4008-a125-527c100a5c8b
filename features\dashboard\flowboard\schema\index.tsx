import z from 'zod';
export const boardSchema = z.object({
  name: z.string().min(3).max(32),
});
export type TBoardSchema = z.infer<typeof boardSchema>;

export const columnSchema = z.object({
  title: z.string().min(3).max(32),
});
export type TColumnSchema = z.infer<typeof columnSchema>;

export const taskSchema = z.object({
  title: z.string().min(3).max(32),
  description: z.string().optional(),
  priority: z.enum(['low', 'medium', 'high']),
  assignedTo: z.string(),
  dueDate: z.date().optional(),
});
export type TTaskSchema = z.infer<typeof taskSchema>;
