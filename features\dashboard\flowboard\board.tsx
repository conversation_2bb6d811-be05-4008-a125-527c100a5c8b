'use client';

import { useMutation, useQuery } from 'convex/react';
import { format } from 'date-fns';
import { GripVertical } from 'lucide-react';
import { useCallback, useState } from 'react';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { Badge2 } from '@/components/ui/badge2';
import { Button } from '@/components/ui/button';
// import { Button2 } from '@/components/ui/button2';
import {
  Kanban,
  KanbanBoard,
  KanbanColumn,
  KanbanColumnContent,
  KanbanColumnHandle,
  KanbanItem,
  KanbanItemHandle,
  KanbanOverlay,
} from '@/components/ui/kanban';
import { api } from '@/convex/_generated/api';
import type { Id } from '@/convex/_generated/dataModel';
import TaskColumnActions from './column-actions';
import CreateOrUpdateTask from './create-edit-task';

interface Task {
  assignee: string | undefined;
  assignedTo: string | undefined;
  assigneeAvatar: string | null | undefined;
  _id: Id<'tasks'>;
  _creationTime: number;
  description?: string | undefined;
  updatedAt?: number | undefined;
  dueDate?: string | undefined;
  title: string;
  createdAt: number;
  columnId: Id<'taskColumns'>;
  priority: 'low' | 'medium' | 'high';
}

// Get column title by id from the columns array
const getColumnTitle = (
  columnId: string,
  columns: Array<{
    _id: string;
    _creationTime: number;
    updatedAt?: number;
    title: string;
    createdAt: number;
  }>
): string => {
  return columns.find((col) => col._id === columnId)?.title || columnId;
};

interface TaskCardProps
  extends Omit<React.ComponentProps<typeof KanbanItem>, 'value' | 'children'> {
  task: Task;
  asHandle?: boolean;
}

function TaskCard({ task, asHandle, ...props }: TaskCardProps) {
  const [openEditDialog, setOpenEditDialog] = useState(false);
  const cardContent = (
    <div
      className="rounded-md border bg-card p-3 shadow-xs"
      onClick={() => setOpenEditDialog(true)}
    >
      <div className="flex flex-col gap-2.5">
        <div className="flex items-center justify-between gap-2">
          <span className="line-clamp-1 font-medium text-sm">{task.title}</span>
          <Badge2
            appearance="outline"
            className="pointer-events-none h-5 shrink-0 rounded-sm px-1.5 text-[11px] capitalize"
            variant={
              task.priority === 'high'
                ? 'destructive'
                : task.priority === 'medium'
                  ? 'primary'
                  : 'warning'
            }
          >
            {task.priority}
          </Badge2>
        </div>
        <div className="flex items-center justify-between text-muted-foreground text-xs">
          {task.assignee && (
            <div className="flex items-center gap-1">
              <Avatar className="size-4">
                <AvatarImage src={task.assigneeAvatar || ''} />
                <AvatarFallback>{task.assignee.charAt(0)}</AvatarFallback>
              </Avatar>
              <span className="line-clamp-1">{task.assignee}</span>
            </div>
          )}
          {task.dueDate && (
            <time className="whitespace-nowrap text-[10px] tabular-nums">
              {format(new Date(task.dueDate), 'dd/MM/yyyy')}
            </time>
          )}
        </div>
      </div>
    </div>
  );

  return (
    <KanbanItem value={task._id} {...props}>
      {asHandle ? (
        <KanbanItemHandle>{cardContent}</KanbanItemHandle>
      ) : (
        cardContent
      )}
      <CreateOrUpdateTask
        assignedTo={task.assignedTo as Id<'users'>}
        columnId={task.columnId}
        dueDate={task.dueDate}
        id={task._id}
        openDialog={openEditDialog}
        priority={task.priority}
        setOpenDialog={setOpenEditDialog}
        title={task.title}
      />
    </KanbanItem>
  );
}

interface TaskColumnProps
  extends Omit<React.ComponentProps<typeof KanbanColumn>, 'children'> {
  tasks: Task[];
  isOverlay?: boolean;
  columns: Array<{
    _id: string;
    _creationTime: number;
    updatedAt?: number;
    title: string;
    createdAt: number;
  }>;
}

function TaskColumn({
  value,
  tasks,
  isOverlay,
  columns,
  ...props
}: TaskColumnProps) {
  return (
    <KanbanColumn
      value={value}
      {...props}
      className="rounded-md border bg-card p-2.5 shadow-xs"
    >
      <div className="mb-2.5 flex items-center justify-between">
        <div className="flex items-center gap-1">
          <KanbanColumnHandle asChild className="opacity-50">
            <Button size="sm" variant="dim">
              <GripVertical />
            </Button>
          </KanbanColumnHandle>
          <div className="flex items-center gap-2.5">
            <span className="font-semibold text-sm">
              {getColumnTitle(value, columns)}
            </span>
            <Badge2 variant="secondary">{tasks.length}</Badge2>
          </div>
        </div>
        <TaskColumnActions
          columnId={value as Id<'taskColumns'>}
          title={getColumnTitle(value, columns)}
        />
      </div>
      <KanbanColumnContent
        className="flex flex-col gap-2.5 p-0.5"
        value={value}
      >
        {tasks.map((task) => (
          <TaskCard asHandle={!isOverlay} key={task._id} task={task} />
        ))}
        {tasks.length === 0 && (
          <div className="flex min-h-[300px] items-center justify-center">
            <CreateOrUpdateTask columnId={value as Id<'taskColumns'>} />
          </div>
        )}
      </KanbanColumnContent>
    </KanbanColumn>
  );
}

export default function KanbanFlowboard() {
  const tasksResult = useQuery(api.board.getAllTasks);
  const columnsResult = useQuery(api.board.getAllColumns);
  const moveTaskMutation = useMutation(api.board.moveTask);

  // Handle the case where the query returns an error object
  const tasks = Array.isArray(tasksResult) ? tasksResult : [];
  const columns = Array.isArray(columnsResult) ? columnsResult : [];

  // Group tasks by columnId, ensuring all columns are included even if empty
  const columnTasks = columns.reduce(
    (acc, column) => {
      acc[column._id] = [];
      return acc;
    },
    {} as Record<string, Task[]>
  );

  // Add tasks to their respective columns
  for (const task of tasks) {
    if (columnTasks[task.columnId]) {
      columnTasks[task.columnId].push(task);
    }
  }

  const handleValueChange = useCallback(
    async (newColumns: Record<string, Task[]>) => {
      // Find tasks that have changed columns
      const currentTasks = tasks.reduce(
        (acc, task) => {
          acc[task._id] = task.columnId;
          return acc;
        },
        {} as Record<string, string>
      );

      const newTasks = Object.entries(newColumns).flatMap(
        ([columnId, tasksInColumn]) =>
          tasksInColumn.map((task) => ({ ...task, columnId }))
      );

      // Update tasks that have moved to different columns
      const movePromises = newTasks
        .filter((newTask) => currentTasks[newTask._id] !== newTask.columnId)
        .map((newTask) =>
          moveTaskMutation({
            id: newTask._id as Id<'tasks'>,
            columnId: newTask.columnId as Id<'taskColumns'>,
          }).catch(() => {
            // Handle error silently or show toast notification
            return null;
          })
        );

      await Promise.all(movePromises);
    },
    [tasks, moveTaskMutation]
  );

  return (
    <Kanban
      getItemValue={(item) => item._id}
      onValueChange={handleValueChange}
      value={columnTasks}
    >
      <KanbanBoard className="grid auto-rows-fr grid-cols-1 md:grid-cols-2 lg:grid-cols-3">
        {Object.entries(columnTasks).map(([columnValue, columnTaskList]) => (
          <TaskColumn
            columns={columns}
            key={columnValue}
            tasks={columnTaskList}
            value={columnValue}
          />
        ))}
      </KanbanBoard>
      <KanbanOverlay>
        <div className="size-full rounded-md bg-muted/60" />
      </KanbanOverlay>
    </Kanban>
  );
}
