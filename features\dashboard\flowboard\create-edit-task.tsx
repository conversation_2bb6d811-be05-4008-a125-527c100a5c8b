'use client';
import { DialogDescription } from '@ariakit/react';
import { zodResolver } from '@hookform/resolvers/zod';
import { useMutation, useQuery } from 'convex/react';
import { format } from 'date-fns';
import { CalendarIcon, PlusIcon } from 'lucide-react';
import { useEffect, useState } from 'react';
import { useForm } from 'react-hook-form';
import { toast } from 'sonner';
import { Spinner } from '@/components/custom/spinner';
import { Button } from '@/components/ui/button';
import { Calendar } from '@/components/ui/calendar';
import {
  Dialog,
  DialogClose,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '@/components/ui/dialog';
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form';
import { Input } from '@/components/ui/input';
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from '@/components/ui/popover';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { api } from '@/convex/_generated/api';
import type { Id } from '@/convex/_generated/dataModel';
import { cn } from '@/lib/utils';
import { type TTaskSchema, taskSchema } from './schema';

interface CreateOrUpdateTaskProps {
  openDialog?: boolean;
  setOpenDialog?: (open: boolean) => void;
  title?: string;
  description?: string;
  priority?: 'low' | 'medium' | 'high';
  assignedTo?: Id<'users'>;
  dueDate?: string;
  id?: Id<'tasks'>;
  columnId: Id<'taskColumns'>;
}

export default function CreateOrUpdateTask({
  openDialog,
  setOpenDialog,
  title,
  description,
  priority,
  assignedTo,
  dueDate,
  id,
  columnId,
}: CreateOrUpdateTaskProps) {
  const isEditMode = Boolean(id);
  const createTaskMutation = useMutation(api.board.createTask);
  const updateTaskMutation = useMutation(api.board.updateTask);
  const users = useQuery(api.users.allUsersExceptCurrent);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [internalOpen, setInternalOpen] = useState(false);

  const open = openDialog ?? internalOpen;
  const setOpen = setOpenDialog ?? setInternalOpen;

  const form = useForm<TTaskSchema>({
    resolver: zodResolver(taskSchema),
    defaultValues: {
      title: title || '',
      description: description || '',
      priority: priority || 'low',
      assignedTo: assignedTo || '',
      dueDate: dueDate ? new Date(dueDate) : undefined,
    },
  });
  useEffect(() => {
    if (open) {
      form.reset({
        title: title || '',
        description: description || '',
        priority: priority || 'low',
        assignedTo: assignedTo || '',
        dueDate: dueDate ? new Date(dueDate) : undefined,
      });
    }
  }, [open, title, description, priority, assignedTo, dueDate, form]);

  const formatTaskData = (data: TTaskSchema) => ({
    title: data.title,
    description: data.description,
    priority: data.priority,
    dueDate: data.dueDate
      ? data.dueDate.toISOString().split('T')[0]
      : undefined,
    assignedTo: data.assignedTo as Id<'users'>,
  });

  const handleTaskResult = (
    result: { success: boolean; error?: string },
    successMessage: string
  ) => {
    if (result?.success) {
      form.reset();
      toast.success(successMessage);
    } else {
      toast.error(result.error);
    }
  };

  const handleUpdateTask = async (data: TTaskSchema) => {
    if (!id) {
      toast.error('Failed to get task id.');
      return;
    }

    const result = await updateTaskMutation({
      id,
      ...formatTaskData(data),
    });
    handleTaskResult(result, 'Task updated successfully!');
  };

  const handleCreateTask = async (data: TTaskSchema) => {
    const result = await createTaskMutation({
      columnId,
      ...formatTaskData(data),
    });
    handleTaskResult(result, 'Task created successfully!');
  };

  const handleSubmit = async (data: TTaskSchema) => {
    setIsSubmitting(true);
    setOpen(false);

    try {
      if (isEditMode) {
        await handleUpdateTask(data);
      } else {
        await handleCreateTask(data);
      }
    } catch {
      toast.error('Something went wrong.');
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <Dialog onOpenChange={setOpen} open={open}>
      {!isEditMode && (
        <DialogTrigger asChild>
          <Button
            className="size-14 rounded-full"
            size={'lg'}
            variant={'secondary'}
          >
            <PlusIcon />
          </Button>
        </DialogTrigger>
      )}

      <DialogContent className="flex w-full max-w-md flex-col">
        <DialogHeader className="">
          <DialogTitle>{isEditMode ? 'Update' : 'Create New'} Task</DialogTitle>
          <DialogDescription>
            {isEditMode
              ? 'Update your task to this column.'
              : 'Add a new task to this column.'}{' '}
            Task
          </DialogDescription>
        </DialogHeader>
        <Form {...form}>
          <form
            className="mt-6 space-y-4"
            onSubmit={form.handleSubmit(handleSubmit)}
          >
            <FormField
              control={form.control}
              name="title"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Title</FormLabel>
                  <FormControl>
                    <Input placeholder="Title" {...field} />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
            <FormField
              control={form.control}
              name="description"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Description</FormLabel>
                  <FormControl>
                    <Input placeholder="Description" {...field} />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
            <FormField
              control={form.control}
              name="priority"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Priority</FormLabel>
                  <FormControl>
                    <Select onValueChange={field.onChange} value={field.value}>
                      <FormControl>
                        <SelectTrigger className="w-full cursor-pointer dark:bg-background">
                          <SelectValue
                            className="w-full dark:bg-background"
                            placeholder="Select a priority"
                          />
                        </SelectTrigger>
                      </FormControl>
                      <SelectContent>
                        <SelectItem value="low">Low</SelectItem>
                        <SelectItem value="medium">Medium</SelectItem>
                        <SelectItem value="high">High</SelectItem>
                      </SelectContent>
                    </Select>
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
            {/* select user to assign to */}
            <FormField
              control={form.control}
              name="assignedTo"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Assignee</FormLabel>
                  <FormControl>
                    <Select onValueChange={field.onChange} value={field.value}>
                      <FormControl>
                        <SelectTrigger className="w-full cursor-pointer dark:bg-background">
                          <SelectValue
                            className="w-full dark:bg-background"
                            placeholder="Select an assignee"
                          />
                        </SelectTrigger>
                      </FormControl>
                      <SelectContent>
                        {users?.map((user) => (
                          <SelectItem
                            className="cursor-pointer"
                            key={user._id}
                            value={user._id}
                          >
                            {user.name}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
            <FormField
              control={form.control}
              name="dueDate"
              render={({ field }) => (
                <FormItem className="flex flex-col">
                  <FormLabel>Due Date</FormLabel>
                  <Popover>
                    <PopoverTrigger asChild>
                      <FormControl>
                        <Button
                          className={cn(
                            'w-full pl-3 text-left font-normal',
                            !field.value && 'text-muted-foreground'
                          )}
                          variant={'outline'}
                        >
                          {field.value ? (
                            format(field.value, 'PPP')
                          ) : (
                            <span>Pick a date</span>
                          )}
                          <CalendarIcon className="ml-auto h-4 w-4 opacity-50" />
                        </Button>
                      </FormControl>
                    </PopoverTrigger>
                    <PopoverContent align="start" className="w-auto p-0">
                      <Calendar
                        captionLayout="dropdown"
                        disabled={(date) =>
                          date > new Date() || date < new Date('1900-01-01')
                        }
                        mode="single"
                        onSelect={field.onChange}
                        selected={field.value}
                      />
                    </PopoverContent>
                  </Popover>
                  <FormDescription>When is this task due?</FormDescription>
                  <FormMessage />
                </FormItem>
              )}
            />
            <Button className="w-full" disabled={isSubmitting} type="submit">
              {isSubmitting ? (
                <Spinner text="Submitting..." />
              ) : isEditMode ? (
                'Update Task'
              ) : (
                'Create Task'
              )}
            </Button>
          </form>
        </Form>
        <DialogClose />
      </DialogContent>
    </Dialog>
  );
}
